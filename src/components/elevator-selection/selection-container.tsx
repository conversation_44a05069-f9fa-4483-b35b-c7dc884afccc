import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { SelectionForm } from "./selection-form";
import { SelectionResult } from "./selection-result";
import { CalculatorLog } from "./calculator-log";
import {
  ElevatorSelectionData,
  CalculatedResult,
  getElevatorSelection,
  saveElevatorSelection,
  getPresetDefaultSelectionData,
  Shaft,
} from "@/lib/elevator-selection";
import { OpportunityData } from "@/components/dashboard/opportunity-card";
import { calculateElevatorRecommend } from "@/api/calculate/api";
import { toast } from "sonner";

interface SelectionContainerProps {
  opportunityData: OpportunityData;
  onSave: () => void;
}

export function SelectionContainer({ opportunityData, onSave }: SelectionContainerProps) {
  // 页面加载状态
  const [isInitializing, setIsInitializing] = useState(true);

  // 获取已保存的选型数据或使用预设默认数据
  const [selectionData, setSelectionData] = useState<ElevatorSelectionData>(() => {
    // 先返回一个空的初始状态，在useEffect中进行真正的初始化
    return getPresetDefaultSelectionData(opportunityData.id);
  });

  const [recommendWarnings, setRecommendWarnings] = useState<string[]>([]);
  const [recommendShaftData, setRecommendShaftData] = useState<Shaft | null>(null);
  const [showResult, setShowResult] = useState(false);

  // 计算结果
  const calculatedResult: CalculatedResult = {
    capacity: selectionData.capacity,
    persons: Math.floor(selectionData.capacity / 75),
    shaftWidth: selectionData.shaftWidth,
    shaftDepth: selectionData.shaftDepth,
    overheadHeight: selectionData.overhead,
    pitDepth: selectionData.pitDepth,
  };

  // 更新选型数据
  const handleChange = (partialData: Partial<ElevatorSelectionData>) => {
    setSelectionData(prev => ({
      ...prev,
      ...partialData,
      lastUpdated: new Date().toISOString(),
    }));
  };

  // 自动获取推荐值
  const fetchRecommendValues = async (data: ElevatorSelectionData) => {
    try {
      console.log("调用推荐接口，传入的井道参数:", {
        shaftWidth: data.shaftWidth,
        shaftDepth: data.shaftDepth,
        overhead: data.overhead,
        pitDepth: data.pitDepth
      });

      const baseData = {
        Lift_Model: data.liftModel,
        Capacity: data.capacity,
        Speed: data.speed,
        Travel_Height: data.travelHeight,
        Car_Width: data.carWidth,
        Car_Depth: data.carDepth,
        Car_Height: data.carHeight,
        CWT_Position: data.cwtPosition,
        CWT_Safety_Gear: data.cwtSafetyGear ? "Yes" : "No",
        Door_Opening: data.doorOpening,
        Door_Width: data.doorWidth,
        Door_Height: data.doorHeight,
        Through_Door: data.throughDoor ? "Yes" : "No",
        Glass_Door: data.glassDoor ? "Yes" : "No",
        Standard: data.standard,
        Door_Center_from_Car_Center: data.doorCenterPosition,
        Car_Area_Exceed_the_Code: data.floorExceedCode ? "Yes" : "No Allow",
        Shaft_Tolerance: data.shaftTolerance,
        Marble_Floor: data.marbleFloorThickness,
        Shaft_Width: data.shaftWidth,
        Shaft_Depth: data.shaftDepth,
        Overhead: data.overhead,
        Pit_Depth: data.pitDepth,
      };

      console.log("发送给推荐接口的完整数据:", baseData);

      const { data: recommend } = await calculateElevatorRecommend(baseData);

      const recommendData = {
        shaftWidth: recommend.Shaft_Width_min,
        shaftDepth: recommend.Shaft_Depth_min,
        overheadHeight: recommend.Shaft_Height_min,
        pitDepth: recommend.Shaft_Pit_min,
      };

      setRecommendShaftData(recommendData);

      // 创建包含推荐值的完整数据
      const updatedData = {
        ...data,
        shaftWidth: recommend.Shaft_Width_min,
        shaftDepth: recommend.Shaft_Depth_min,
        overhead: recommend.Shaft_Height_min,
        pitDepth: recommend.Shaft_Pit_min,
        lastUpdated: new Date().toISOString(),
      };

      // 更新选型数据
      setSelectionData(updatedData);

      // 保存到localStorage
      saveElevatorSelection(updatedData);

      // 处理警告信息
      if (recommend.warn && recommend.warning_text?.length > 0) {
        setRecommendWarnings(recommend.warning_text);
      } else {
        setRecommendWarnings([]);
      }

      return { success: true, data: updatedData };
    } catch (error) {
      console.error("获取推荐井道参数失败", error);
      toast.error("获取推荐井道参数失败，请稍后重试");
      return { success: false, data: null };
    }
  };

  // 页面初始化
  useEffect(() => {
    const initializePage = async () => {
      try {
        // 临时：强制使用新的预设默认值，忽略localStorage中的旧数据
        console.log("开始初始化页面，商机ID:", opportunityData.id);

        // 获取新的预设默认数据
        const presetData = getPresetDefaultSelectionData(opportunityData.id);
        console.log("预设默认数据:", presetData);
        console.log("预设井道参数:", {
          shaftWidth: presetData.shaftWidth,
          shaftDepth: presetData.shaftDepth,
          overhead: presetData.overhead,
          pitDepth: presetData.pitDepth
        });

        // 设置预设数据
        setSelectionData(presetData);

        // 使用预设数据调用推荐接口
        const result = await fetchRecommendValues(presetData);

        // 只有在推荐接口成功返回后才去除loading
        if (result.success) {
          setIsInitializing(false);
        } else {
          // 如果推荐接口失败，也要去除loading，但显示错误状态
          setIsInitializing(false);
          toast.error("初始化失败，请刷新页面重试");
        }
      } catch (error) {
        console.error("页面初始化失败:", error);
        setIsInitializing(false);
        toast.error("页面初始化失败，请刷新页面重试");
      }
    };

    initializePage();
  }, [opportunityData.id]); // 只在组件挂载时执行一次

  // 保存选型数据
  const handleSave = () => {
    // 设置提交标志和时间
    const dataToSave = {
      ...selectionData,
      isSubmitted: true,
      submittedAt: new Date().toISOString(),
    };
    saveElevatorSelection(dataToSave);
    setSelectionData(dataToSave); // 更新本地状态
    onSave();
  };

  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return (
      <div className="space-y-4 max-w-6xl mx-auto p-4 relative">
        {/* 顶部标题 - 公司标志和标题 */}
        <div className="flex items-center justify-center mb-4">
          <div className="text-center">
            <h1 className="text-xl font-bold mb-1">电梯土建尺寸规划</h1>
            <p className="text-md">Reassuring Planning Tool (RPT)</p>
          </div>
          <div className="w-32"></div> {/* 右侧占位 */}
        </div>

        {/* 项目信息展示 */}
        <div className="flex flex-col space-y-2 mb-4">
          <div className="flex items-center">
            <div className="w-28 font-semibold">项目名称:</div>
            <div>{opportunityData?.projectName || "N/A"}</div>
          </div>
          <div className="flex items-center">
            <div className="w-28 font-semibold">项目编号:</div>
            <div>{opportunityData?.projectCode || "N/A"}</div>
          </div>
          <div className="flex items-center">
            <div className="w-28 font-semibold">客户名称:</div>
            <div>{opportunityData?.customerName || "N/A"}</div>
          </div>
        </div>

        {/* 加载状态 */}
        <div className="relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-[#00B4AA]/5 rounded-2xl shadow-xl border border-gray-100">
          {/* 背景装饰 */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-[#00B4AA]/10 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full blur-3xl"></div>
          </div>

          <div className="relative z-10 p-16 text-center">
            <div className="flex flex-col items-center justify-center space-y-8">
              {/* 动画加载图标 */}
              <div className="relative">
                {/* 外圈旋转环 */}
                <div className="w-20 h-20 border-4 border-gray-200 rounded-full animate-spin">
                  <div className="w-full h-full border-4 border-transparent border-t-[#00B4AA] border-r-[#00B4AA] rounded-full"></div>
                </div>
                {/* 内圈脉冲点 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-3 h-3 bg-[#00B4AA] rounded-full animate-pulse"></div>
                </div>
              </div>

              {/* 文字内容 */}
              <div className="space-y-4 max-w-md">
                <h3 className="text-2xl font-bold text-gray-900 tracking-tight">
                  正在初始化电梯选型系统
                </h3>
                <div className="space-y-2">
                  <p className="text-base text-gray-600 leading-relaxed">
                    正在为您加载预设配置参数
                  </p>
                  <p className="text-sm text-gray-500">
                    系统正在获取最优井道推荐参数，请稍候片刻...
                  </p>
                </div>
              </div>

              {/* 进度指示器 */}
              <div className="w-64 bg-gray-200 rounded-full h-2 overflow-hidden">
                <div className="h-full bg-gradient-to-r from-[#00B4AA] to-blue-500 rounded-full animate-pulse"></div>
              </div>

              {/* 底部提示 */}
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <div className="w-2 h-2 bg-[#00B4AA] rounded-full animate-bounce"></div>
                <span>美的电梯智能选型系统</span>
                <div className="w-2 h-2 bg-[#00B4AA] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 max-w-6xl mx-auto p-4 relative">
      {/* 顶部标题 - 公司标志和标题 */}
      <div className="flex items-center justify-center mb-4">
        <div className="text-center">
          <h1 className="text-xl font-bold mb-1">电梯土建尺寸规划</h1>
          <p className="text-md">Reassuring Planning Tool (RPT)</p>
        </div>
        <div className="w-32"></div> {/* 右侧占位 */}
      </div>

      {/* 项目信息展示 */}
      <div className="flex flex-col space-y-2 mb-4">
        <div className="flex items-center">
          <div className="w-28 font-semibold">项目名称:</div>
          <div>{opportunityData?.projectName || "N/A"}</div>
        </div>
        <div className="flex items-center">
          <div className="w-28 font-semibold">项目编号:</div>
          <div>{opportunityData?.projectCode || "N/A"}</div>
        </div>
        <div className="flex items-center">
          <div className="w-28 font-semibold">客户名称:</div>
          <div>{opportunityData?.customerName || "N/A"}</div>
        </div>
      </div>

      {/* 选型表单 */}
      <SelectionForm
        data={selectionData}
        setRecommendWarnings={setRecommendWarnings}
        recommendShaftData={recommendShaftData}
        setRecommendShaftData={setRecommendShaftData}
        onChange={handleChange}
        setShowResult={setShowResult}
      />

      {/* 选型结果 */}
        <SelectionResult
          result={calculatedResult}
          recommendShaftData={recommendShaftData}
          recommendWarnings={recommendWarnings}
        />

      {/* 计算器日志 - 浮动在右下角 */}
      <CalculatorLog data={selectionData} />
    </div>
  );
}
