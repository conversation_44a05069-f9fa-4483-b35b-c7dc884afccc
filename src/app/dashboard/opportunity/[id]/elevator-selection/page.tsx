'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { SelectionContainer } from '@/components/elevator-selection';
import { hasElevatorSelection } from '@/lib/elevator-selection';
import { OpportunityData } from '@/components/dashboard/opportunity-card';
import { ArrowLeft } from 'lucide-react';

// 从localStorage获取商机数据
const getOpportunityDataFromStorage = (id: string): OpportunityData | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const data = localStorage.getItem('opportunities');
    if (!data) return null;
    
    const opportunities = JSON.parse(data);
    return opportunities.find((opp: OpportunityData) => opp.id === id) || null;
  } catch (error) {
    console.error('Error getting opportunity data from localStorage:', error);
    return null;
  }
};

// 加载状态组件
function LoadingState() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-[#00B4AA]/5 rounded-2xl shadow-xl border border-gray-100 p-16">
        {/* 背景装饰 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-[#00B4AA]/10 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 text-center">
          <div className="flex flex-col items-center justify-center space-y-8">
            {/* 动画加载图标 */}
            <div className="relative">
              {/* 外圈旋转环 */}
              <div className="w-20 h-20 border-4 border-gray-200 rounded-full animate-spin">
                <div className="w-full h-full border-4 border-transparent border-t-[#00B4AA] border-r-[#00B4AA] rounded-full"></div>
              </div>
              {/* 内圈脉冲点 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-3 h-3 bg-[#00B4AA] rounded-full animate-pulse"></div>
              </div>
            </div>

            {/* 文字内容 */}
            <div className="space-y-4 max-w-md">
              <h3 className="text-2xl font-bold text-gray-900 tracking-tight">
                正在加载商机数据
              </h3>
              <div className="space-y-2">
                <p className="text-base text-gray-600 leading-relaxed">
                  正在获取项目信息和选型数据
                </p>
                <p className="text-sm text-gray-500">
                  请稍候片刻...
                </p>
              </div>
            </div>

            {/* 进度指示器 */}
            <div className="w-64 bg-gray-200 rounded-full h-2 overflow-hidden">
              <div className="h-full bg-gradient-to-r from-[#00B4AA] to-blue-500 rounded-full animate-pulse"></div>
            </div>

            {/* 底部提示 */}
            <div className="flex items-center space-x-2 text-xs text-gray-400">
              <div className="w-2 h-2 bg-[#00B4AA] rounded-full animate-bounce"></div>
              <span>美的电梯智能选型系统</span>
              <div className="w-2 h-2 bg-[#00B4AA] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 错误状态组件
function ErrorState({ onBack }: { onBack: () => void }) {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-16 text-center max-w-md">
        <div className="space-y-6">
          {/* 错误图标 */}
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>

          {/* 错误信息 */}
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-gray-900">无法加载商机数据</h3>
            <p className="text-gray-600">
              抱歉，无法找到相关的商机信息。可能是数据已被删除或链接有误。
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button
              onClick={onBack}
              className="w-full bg-[#00B4AA] hover:bg-[#00B4AA]/90 text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回上一页
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/dashboard'}
              className="w-full"
            >
              返回首页
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// 电梯选型页面组件
export default function ElevatorSelectionPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  
  const [opportunityData, setOpportunityData] = useState<OpportunityData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 获取商机数据
    if (id) {
      setLoading(true);
      const data = getOpportunityDataFromStorage(id);
      setOpportunityData(data);
      setLoading(false);
    }
  }, [id]);

  const handleSave = () => {
    // 保存成功后返回商机详情页
    router.push(`/dashboard/opportunity/${id}`);
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return <LoadingState />;
  }

  if (!opportunityData) {
    return <ErrorState onBack={handleBack} />;
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex items-center mb-6">
        <Button variant="ghost" className="mr-4" onClick={handleBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回
        </Button>
        <h1 className="text-2xl font-bold">电梯选型 - {opportunityData.projectName}</h1>
      </div>

      <SelectionContainer 
        opportunityData={opportunityData} 
        onSave={handleSave} 
      />
    </div>
  );
}